/* ECOTrac Design System - Component Library */

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: var(--color-background);
}

/* ===== BUTTON COMPONENTS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-sm);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-all);
  background: var(--color-primary);
  color: var(--color-text-on-primary);
}

.btn:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:focus {
  outline: var(--focus-ring-width) solid var(--focus-ring);
  outline-offset: var(--focus-ring-offset);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Button Variants */
.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-text-on-primary);
}

.btn-secondary:hover {
  background: var(--color-primary);
}

.btn-success {
  background: var(--color-success);
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn-warning {
  background: var(--color-warning);
  color: white;
}

.btn-warning:hover {
  background: #d97706;
}

.btn-danger {
  background: var(--color-error);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-ghost {
  background: transparent;
  color: var(--brand-900);
  border: 1px solid var(--brand-900);
}

.btn-ghost:hover {
  background: var(--brand-900);
  color: white;
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-lg);
}

.btn-full {
  width: 100%;
}

/* ===== FORM COMPONENTS ===== */
.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  background-color: var(--color-background);
  color: var(--color-text-primary);
  transition: var(--transition-all);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 2px var(--focus-ring);
}

.form-textarea {
  resize: vertical;
  min-height: calc(var(--space-md) * 3);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--color-surface-light);
}

/* Form Input Sizes */
.form-input-sm,
.form-select-sm {
  padding: var(--space-xs);
  font-size: var(--font-size-sm);
}

.form-input-lg,
.form-select-lg {
  padding: var(--space-md);
  font-size: var(--font-size-lg);
}

/* ===== CARD COMPONENTS ===== */
.card {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  transition: var(--transition-all);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-md);
  background: var(--color-surface-light);
  border-bottom: 1px solid var(--color-border);
}

.card-body {
  padding: var(--space-md);
}

.card-footer {
  padding: var(--space-md);
  background: var(--color-surface-light);
  border-top: 1px solid var(--color-border);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.card-subtitle {
  margin: var(--space-xs) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Card Variants */
.card-bordered {
  border: 1px solid var(--color-border);
}

.card-elevated {
  box-shadow: var(--shadow-lg);
}

.card-flat {
  box-shadow: none;
  border: 1px solid var(--color-border);
}

/* ===== HEADER COMPONENT ===== */
.header {
  background: var(--brand-900);
  color: var(--color-text-on-dark);
  padding: var(--space-sm) var(--space-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-header);
  min-height: 80px;
}

.header-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin: 0;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.logo {
  width: 316px;
  height: 60px;
  object-fit: contain;
}

/* ===== SIDEBAR COMPONENTS ===== */
.sidebar {
  position: fixed;
  top: 80px;
  left: 60px;
  width: 320px;
  height: calc(100vh - 140px);
  background: var(--color-surface);
  border-right: 2px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-panel);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
  z-index: var(--z-fixed);
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  background: var(--color-background-light);
  padding: var(--space-md);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-title {
  color: var(--color-text-primary);
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.sidebar-content {
  padding: var(--space-md);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-all);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--color-error);
  color: white;
}

/* ===== NAVIGATION COMPONENTS ===== */
.nav-column {
  width: 60px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-md) 0;
  gap: var(--space-md);
  z-index: var(--z-sticky);
}

.nav-btn {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  border: none;
  background: var(--color-surface);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  transition: var(--transition-all);
  box-shadow: var(--shadow-sm);
  color: var(--color-text-primary);
}

.nav-btn:hover {
  background: var(--brand-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-btn.active {
  background: var(--brand-600);
  color: white;
}

/* ===== LIST COMPONENTS ===== */
.list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.list-item {
  background: var(--color-surface-light);
  padding: var(--space-sm);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  border: 2px solid transparent;
  border-left: 4px solid var(--color-success);
  cursor: pointer;
  transition: var(--transition-all);
}

.list-item:hover {
  background: var(--color-surface);
  transform: translateX(2px);
}

.list-item.selected {
  background: #e8f4fd;
  border-left-color: var(--color-info);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.list-item-title {
  margin-bottom: var(--space-xs);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.list-item-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

/* ===== FOOTER COMPONENTS ===== */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-md);
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  z-index: var(--z-dropdown);
}

.footer-info {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.info-item .icon {
  font-size: var(--font-size-lg);
}

.info-item .value {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* ===== PANEL COMPONENTS ===== */
.panel {
  position: fixed;
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  box-shadow: var(--shadow-panel);
  transform: translateY(100%);
  transition: transform var(--transition-slow);
  z-index: var(--z-dropdown);
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.panel.open {
  transform: translateY(0);
}

.panel-header {
  background: var(--color-background-light);
  padding: var(--space-md);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  color: var(--color-text-primary);
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.panel-content {
  padding: var(--space-md);
}

/* ===== GRID COMPONENTS ===== */
.grid {
  display: grid;
  gap: var(--space-md);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
.grid-auto { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* ===== INFO CARD COMPONENT ===== */
.info-card {
  background: var(--color-background-light);
  padding: var(--space-md);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--brand-600);
}

.info-card-title {
  margin: 0 0 var(--space-sm) 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.info-card-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--brand-600);
  margin: 0;
}

/* ===== LOADING & ERROR STATES ===== */
.loading {
  text-align: center;
  padding: var(--space-xl);
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: var(--space-md);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-md);
  border-left: 4px solid var(--color-error);
}

.success {
  background: #d1edff;
  color: #0c4a6e;
  padding: var(--space-md);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-md);
  border-left: 4px solid var(--color-success);
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .nav-column {
    width: 50px;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }

  .sidebar {
    left: 50px;
    width: 280px;
  }

  .footer-info {
    gap: var(--space-md);
  }

  .info-item {
    font-size: var(--font-size-xs);
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .header-title {
    font-size: var(--font-size-xl);
  }

  .logo {
    width: 250px;
    height: 48px;
  }
}
