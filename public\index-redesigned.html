<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracker GPS Teltonika - Mappa Live</title>
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <!-- Single import for the entire design system -->
    <link rel="stylesheet" href="design-system.css">
    <style>
        /* Minimal custom styles - most styling now comes from design system */
        .main-content {
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 50px;
            display: flex;
        }

        .map-container {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 1;
        }

        #map {
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* Vehicle list specific styles */
        .vehicles-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }

        .vehicle-marker {
            background: none !important;
        }

        /* Footer expanded panel */
        .footer-expanded-panel {
            position: fixed;
            bottom: 50px;
            left: 0;
            right: 0;
            height: 200px;
            background: var(--color-surface);
            border-top: 2px solid var(--color-border);
            box-shadow: var(--shadow-panel);
            transform: translateY(100%);
            transition: transform var(--transition-slow);
            z-index: var(--z-dropdown);
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .footer-expanded-panel.open {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Header using design system components -->
    <div class="header">
        <h1 class="header-title">
            <img src="images/logo-ecotrac-bianco.png" alt="Logo" class="logo">
        </h1>
        <div class="header-nav">
            <span id="username" class="text-on-dark"></span>
            <button class="btn btn-danger hidden" onclick="logout()" id="logoutBtn">Esci</button>
        </div>
    </div>

    <!-- Login Form using design system -->
    <div id="loginSection" class="login-layout">
        <div class="login-card">
            <div class="text-center mb-md">
                <img src="images/logo-ecotrac-verde.png" alt="Logo" class="logo">
            </div>
            <h2 class="text-center mb-xl text-secondary">Accedi al sistema</h2>
            <div id="loginError" class="error hidden"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="loginUsername">Nome utente:</label>
                    <input type="text" id="loginUsername" name="username" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="loginPassword">Password:</label>
                    <input type="password" id="loginPassword" name="password" class="form-input" required>
                </div>
                <button type="submit" class="btn btn-full">Accedi</button>
            </form>
        </div>
    </div>

    <!-- Main Application using design system components -->
    <div id="mainApp" class="hidden">
        <div class="main-content">
            <!-- Navigation Column using design system -->
            <div class="nav-column">
                <button type="button" class="nav-btn" onclick="toggleSidebar('vehicles')" title="Elenco Veicoli">
                    🚗
                </button>
                <button type="button" class="nav-btn" onclick="toggleSidebar('info')" title="Informazioni Sistema">
                    📊
                </button>
            </div>

            <!-- Vehicles Sidebar using design system -->
            <div id="vehiclesSidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">Flotta Veicoli</h3>
                    <button class="close-btn" onclick="closeSidebar('vehicles')">&times;</button>
                </div>
                <div class="sidebar-content">
                    <div id="vehiclesContainer" class="loading">Caricamento veicoli...</div>
                </div>
            </div>

            <!-- Info Sidebar using design system -->
            <div id="infoSidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">Informazioni Sistema</h3>
                    <button class="close-btn" onclick="closeSidebar('info')">&times;</button>
                </div>
                <div class="sidebar-content">
                    <div class="grid grid-auto">
                        <div class="info-card">
                            <h4 class="info-card-title">Veicoli Totali</h4>
                            <div class="info-card-value" id="totalVehicles">-</div>
                        </div>
                        <div class="info-card">
                            <h4 class="info-card-title">Veicoli Online</h4>
                            <div class="info-card-value" id="onlineVehicles">-</div>
                        </div>
                        <div class="info-card">
                            <h4 class="info-card-title">Ultimo Aggiornamento</h4>
                            <div class="info-card-value" id="lastUpdate">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map Area -->
            <div class="flex-1 relative z-1">
                <div class="map-container">
                    <div id="map"></div>
                </div>
            </div>
        </div>

        <!-- Footer using design system -->
        <div class="footer">
            <div class="footer-info">
                <div class="info-item">
                    <span class="icon">🚗</span>
                    <span>Veicoli Online:</span>
                    <span class="value" id="footerOnlineCount">-</span>
                </div>
                <div class="info-item">
                    <span class="icon">📡</span>
                    <span>Ultimo Aggiornamento:</span>
                    <span class="value" id="footerLastUpdate">-</span>
                </div>
                <div class="info-item">
                    <span class="icon">⚡</span>
                    <span>Sistema:</span>
                    <span class="value text-success">Online</span>
                </div>
            </div>
            <button class="btn btn-secondary" onclick="toggleFooterPanel()">
                <span>📈</span>
                <span>Statistiche Avanzate</span>
            </button>
        </div>

        <!-- Expanded Footer Panel using design system -->
        <div id="footerExpandedPanel" class="footer-expanded-panel">
            <div class="panel-header">
                <h3 class="panel-title">Statistiche Avanzate Sistema</h3>
                <button class="close-btn" onclick="closeFooterPanel()">&times;</button>
            </div>
            <div class="panel-content">
                <div class="grid grid-auto">
                    <div class="info-card">
                        <h4 class="info-card-title">Veicoli Totali</h4>
                        <div class="info-card-value" id="expandedTotalVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4 class="info-card-title">Veicoli Online</h4>
                        <div class="info-card-value" id="expandedOnlineVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4 class="info-card-title">Veicoli Offline</h4>
                        <div class="info-card-value" id="expandedOfflineVehicles">-</div>
                    </div>
                    <div class="info-card">
                        <h4 class="info-card-title">Ultimo Aggiornamento</h4>
                        <div class="info-card-value" id="expandedLastUpdate">-</div>
                    </div>
                    <div class="info-card">
                        <h4 class="info-card-title">Connessioni Attive</h4>
                        <div class="info-card-value">24/7</div>
                    </div>
                    <div class="info-card">
                        <h4 class="info-card-title">Uptime Sistema</h4>
                        <div class="info-card-value">99.9%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="toast.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let vehicleMarkers = {};
        let token = localStorage.getItem('token');
        let activeSidebar = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            if (token) {
                verifyToken();
            } else {
                showLogin();
            }
        });

        // Show login form
        function showLogin() {
            document.getElementById('loginSection').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            document.getElementById('logoutBtn').classList.add('hidden');
        }

        // Show main app
        function showMainApp() {
            document.getElementById('loginSection').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            document.getElementById('logoutBtn').classList.remove('hidden');
            initializeMap();
            loadVehicles();
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    document.getElementById('username').textContent = data.data.user.username;
                    showMainApp();
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Accesso fallito. Riprova.');
            }
        });

        // Verify token
        async function verifyToken() {
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('username').textContent = data.data.user.username;
                    showMainApp();
                } else {
                    localStorage.removeItem('token');
                    token = null;
                    showLogin();
                }
            } catch (error) {
                localStorage.removeItem('token');
                token = null;
                showLogin();
            }
        }

        // Logout
        function logout() {
            localStorage.removeItem('token');
            token = null;
            showLogin();
        }

        // Initialize map
        function initializeMap() {
            if (map) return;

            map = L.map('map', {
                zoomControl: true,
                attributionControl: true
            }).setView([54.6872, 25.2797], 7); // Lithuania center

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Adjust map size when panels open/close
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        }

        // Toggle sidebar
        function toggleSidebar(sidebarType) {
            const vehiclesSidebar = document.getElementById('vehiclesSidebar');
            const infoSidebar = document.getElementById('infoSidebar');
            const buttons = document.querySelectorAll('.nav-btn');

            // Close all sidebars
            vehiclesSidebar.classList.remove('open');
            infoSidebar.classList.remove('open');
            buttons.forEach(btn => btn.classList.remove('active'));

            // If clicking the same sidebar, just close it
            if (activeSidebar === sidebarType) {
                activeSidebar = null;
                return;
            }

            // Open the requested sidebar
            activeSidebar = sidebarType;
            if (sidebarType === 'vehicles') {
                vehiclesSidebar.classList.add('open');
                buttons[0].classList.add('active');
            } else if (sidebarType === 'info') {
                infoSidebar.classList.add('open');
                buttons[1].classList.add('active');
            }

            // Adjust map size
            setTimeout(() => {
                if (map) map.invalidateSize();
            }, 300);
        }

        // Close sidebar
        function closeSidebar(sidebarType) {
            if (sidebarType === 'vehicles') {
                document.getElementById('vehiclesSidebar').classList.remove('open');
            } else if (sidebarType === 'info') {
                document.getElementById('infoSidebar').classList.remove('open');
            }

            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            activeSidebar = null;

            // Adjust map size
            setTimeout(() => {
                if (map) map.invalidateSize();
            }, 300);
        }

        // Toggle footer panel
        function toggleFooterPanel() {
            const panel = document.getElementById('footerExpandedPanel');
            panel.classList.toggle('open');
        }

        // Close footer panel
        function closeFooterPanel() {
            document.getElementById('footerExpandedPanel').classList.remove('open');
        }

        // Load vehicles
        async function loadVehicles() {
            try {
                const response = await fetch('/api/gps/vehicles', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    displayVehicles(data.data);
                    updateMap(data.data);
                    updateInfoDisplays(data.data);
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Impossibile caricare i veicoli.');
            }
        }

        // Display vehicles in sidebar
        function displayVehicles(vehicles) {
            const container = document.getElementById('vehiclesContainer');

            if (vehicles.length === 0) {
                container.innerHTML = '<p class="text-muted">Nessun veicolo trovato.</p>';
                return;
            }

            // Sort vehicles by name
            const sortedVehicles = vehicles.sort((a, b) => {
                const nameA = (a.name || `Vehicle ${a.imei}`).toLowerCase();
                const nameB = (b.name || `Vehicle ${b.imei}`).toLowerCase();
                return nameA.localeCompare(nameB);
            });

            container.innerHTML = '<div class="vehicles-list"></div>';
            const listContainer = container.querySelector('.vehicles-list');

            sortedVehicles.forEach(vehicle => {
                const vehicleElement = document.createElement('div');
                vehicleElement.className = 'list-item';
                vehicleElement.onclick = () => selectVehicle(vehicle.id, vehicle);

                vehicleElement.innerHTML = `
                    <div class="list-item-title">${vehicle.name || `Vehicle ${vehicle.imei}`}</div>
                    <div class="list-item-description">${vehicle.description || 'Nessuna descrizione disponibile'}</div>
                `;

                listContainer.appendChild(vehicleElement);
            });
        }

        // Select vehicle
        function selectVehicle(vehicleId, vehicle) {
            // Remove previous selection
            document.querySelectorAll('.list-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Select clicked vehicle
            event.target.closest('.list-item').classList.add('selected');

            // Center map on selected vehicle
            if (vehicle.latitude && vehicle.longitude) {
                const lat = parseFloat(vehicle.latitude);
                const lng = parseFloat(vehicle.longitude);
                map.setView([lat, lng], 16);

                // Highlight selected vehicle marker
                Object.values(vehicleMarkers).forEach(marker => {
                    marker.setOpacity(0.6);
                });

                if (vehicleMarkers[vehicle.imei]) {
                    vehicleMarkers[vehicle.imei].setOpacity(1.0);
                    vehicleMarkers[vehicle.imei].openPopup();
                }
            }

            showToastSuccess(`Veicolo selezionato: ${vehicle.name || vehicle.imei}`);
        }

        // Update map with vehicle positions
        function updateMap(vehicles) {
            // Clear existing markers
            Object.values(vehicleMarkers).forEach(marker => {
                map.removeLayer(marker);
            });
            vehicleMarkers = {};

            const bounds = [];

            vehicles.forEach(vehicle => {
                if (vehicle.latitude && vehicle.longitude) {
                    const lat = parseFloat(vehicle.latitude);
                    const lng = parseFloat(vehicle.longitude);

                    const isOnline = vehicle.device_timestamp &&
                        (new Date() - new Date(vehicle.device_timestamp)) < 300000;

                    const icon = L.divIcon({
                        html: `<div style="background: ${isOnline ? 'var(--color-success)' : 'var(--color-error)'};
                                     width: 12px; height: 12px; border-radius: 50%;
                                     border: 2px solid white; box-shadow: var(--shadow-md);"></div>`,
                        iconSize: [16, 16],
                        className: 'vehicle-marker'
                    });

                    const marker = L.marker([lat, lng], { icon })
                        .bindPopup(`
                            <strong>${vehicle.name || `Vehicle ${vehicle.imei}`}</strong><br>
                            IMEI: ${vehicle.imei}<br>
                            Speed: ${vehicle.speed || 0} km/h<br>
                            Direction: ${vehicle.direction || 0}°<br>
                            Ignition: ${vehicle.ignition_status ? 'ON' : 'OFF'}<br>
                            Last Update: ${vehicle.device_timestamp ?
                                new Date(vehicle.device_timestamp).toLocaleString() : 'Never'}
                        `);

                    marker.addTo(map);
                    vehicleMarkers[vehicle.imei] = marker;
                    bounds.push([lat, lng]);
                }
            });

            // Fit map to show all vehicles
            if (bounds.length > 0) {
                map.fitBounds(bounds, { padding: [20, 20] });
            }
        }

        // Update info displays
        function updateInfoDisplays(vehicles) {
            const now = new Date();
            const onlineVehicles = vehicles.filter(v =>
                v.device_timestamp && (now - new Date(v.device_timestamp)) < 300000
            );

            // Update footer info
            document.getElementById('footerOnlineCount').textContent = onlineVehicles.length;
            document.getElementById('footerLastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleTimeString()
                : '-';

            // Update sidebar info
            document.getElementById('totalVehicles').textContent = vehicles.length;
            document.getElementById('onlineVehicles').textContent = onlineVehicles.length;
            document.getElementById('lastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleTimeString()
                : '-';

            // Update expanded panel info
            document.getElementById('expandedTotalVehicles').textContent = vehicles.length;
            document.getElementById('expandedOnlineVehicles').textContent = onlineVehicles.length;
            document.getElementById('expandedOfflineVehicles').textContent = vehicles.length - onlineVehicles.length;
            document.getElementById('expandedLastUpdate').textContent = vehicles.length > 0 && vehicles[0].device_timestamp
                ? new Date(vehicles[0].device_timestamp).toLocaleString()
                : '-';
        }

        // Auto-refresh vehicles every 30 seconds
        setInterval(() => {
            if (token && !document.getElementById('mainApp').classList.contains('hidden')) {
                loadVehicles();
            }
        }, 30000);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (map) {
                setTimeout(() => {
                    map.invalidateSize();
                }, 100);
            }
        });
    </script>
</body>
</html>
