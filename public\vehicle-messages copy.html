<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - Tracker</title>
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <link rel="stylesheet" href="toast.css">
    <link rel="stylesheet" href="color-system.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .header {
            background: var(--brand-900);
            color: var(--color-text-on-dark);
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.1),
                0 0 40px rgba(0, 0, 0, 0.05);
        }

        .header h1 {
            font-size: 2rem;
            margin-right: 2rem;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 316px;
            height: 60px;
        }

        .btn {
            background: var(--color-primary);
            color: var(--color-text-on-primary);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn:focus {
            outline: 3px solid var(--focus-ring);
            outline-offset: 2px;
        }

        .btn-secondary {
            background: var(--color-secondary);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--color-primary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .card .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card .header-section h2 {
            margin: 0;
            color: #2c3e50;
        }

        .card .divider {
            height: 3px;
            background-color: #eee;
            margin-bottom: 1rem;
        }

        .filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 0.25rem;
            flex-wrap: wrap;
        }

        .filters input, .filters select {
            padding: 0.3rem 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            min-width: 140px;
        }

        .filters label {
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .loading {
            text-align: center;
            padding:  1rem 2rem 1rem 2rem;
            color: #666;
        }

        body {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            flex-shrink: 0;
        }

        .container {
            flex: 1;
            max-width: none;
            margin: 0;
            padding: 0 0 0 0;
            width: 100vw;
        }

        .content-grid {
            display: flex;
            height: 100%;
        }

        .map-container {
            flex: 0 0 var(--map-width, 400px);
            min-width: 200px;
            max-width: calc(100vw - 300px);
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            background: white;
            padding: 5px;
            position: relative;
        }

        .map-container #map {
            height: 100%;
            border-radius: 4px;
        }

        .divider {
            width: 4px;
            cursor: ew-resize;
            background-color: #ddd;
            user-select: none;
            height: 100%;
        }

        .messages-section {
            flex: 1;
            min-width: 300px;
            height: 100%;
        }

        .messages-section .card {
            margin-bottom: 0;
            height: 100%;
            padding: 0;
            display: flex;
            flex-direction: column;
        }

        .messages-section .card .header-section {
            flex-shrink: 0;
            padding: 0.5rem 0.2rem 0 2.0rem;
            margin-bottom: 0;
        }

        .messages-section .card .divider {
            flex-shrink: 0;
            margin: 0 0 0.75rem 1.5rem;
        }

        .messages-section .card .filters {
            flex-shrink: 0;
            margin: 0 0 0 0;
            padding-left: 2.0rem;
        }

        .messages-section .card #messagesContainer {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for flex child scrolling */
        }

        .table-wrapper {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for flex child scrolling */
        }

        .table-scroll-container {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 0; /* Important for flex child scrolling */
        }

        /* Ensure sticky headers work properly */
        .table-scroll-container thead {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* Custom scrollbar styling for better UX */
        .table-scroll-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .table-scroll-container::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }



        @media (max-width: 1024px) {
            .content-grid {
                flex-direction: column;
            }

            .divider {
                display: none;
            }

            .map-container {
                height: 400px;
                order: -1;
                flex: 0 0 auto;
                width: 100%;
                min-width: unset;
                max-width: unset;
            }

            .messages-section {
                flex: 1;
                width: 100%;
            }
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85rem;
            line-height: 1.2;
        }

        th, td {
            padding: 0.5rem 0.7rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            font-size: 0.8rem;
        }

        tbody tr:hover {
            background-color: #f8f9fa;
        }

        tbody tr.selected {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
        }

        .sortable {
            user-select: none;
            position: relative;
        }

        .sort-indicator {
            margin-left: 0.5rem;
            font-size: 0.8rem;
            opacity: 0.6;
        }

        .sortable:hover .sort-indicator {
            opacity: 1;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            margin: 0 0.5rem;
        }

        .hidden {
            display: none;
        }

        .no-data {
            text-align: center;
            color: #666;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <img src="images/logo-ecotrac-bianco.png" alt="Logo" class="logo">
        </h1>
        <button class="btn btn-danger" onclick="window.close()">Chiudi</button>
    </div>

    <div class="container">
        <div id="messageError" class="error" style="display: none;"></div>
        <div id="messageSuccess" class="success" style="display: none;"></div>

        <div class="content-grid">
            <div class="map-container">
                <div id="map" style="height: 100%; border-radius: 4px;"></div>
            </div>
            <div class="divider"></div>
            <div class="messages-section">
                <div class="card">
                    <div class="header-section">
                        <h2 id="vehicleTitle">Messaggi per veicolo</h2>
                    </div>
                    <div class="divider"></div>

                    <div class="filters">
                        <label for="startDate">Data/Ora Inizio:</label>
                        <input type="datetime-local" id="startDate" onchange="validateDateRange()">
                        <label for="endDate">Data/Ora Fine:</label>
                        <input type="datetime-local" id="endDate" onchange="validateDateRange()">
                        <label for="sensorFilter">Filtro Sensori:</label>
                        <input type="text" id="sensorFilter" placeholder="es. io_16">
                        <button class="btn" onclick="loadMessages()">Applica Filtri</button>
                    </div>

                    <div id="messagesContainer" class="loading">Caricamento messaggi...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

<script src="toast.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
        let vehicleId = null;
        let token = null;
        let currentOffset = 0;
        let hasMore = false;
        let limit = 100; // Default limit, can be changed by user
        let map = null;
        let currentMessages = [];
        let selectedRowIndex = null;
        let pathMarkers = []; // Store path markers for interaction

        // Sorting state
        let currentSortField = 'device_timestamp';
        let currentSortOrder = 'DESC';

        // Sensor filter state
        let sensorFilterText = '';

        // Resizer functionality
        let isResizing = false;
        let lastX = 0;

        // Load saved map width
        const savedMapWidth = localStorage.getItem('vehicle-messages-map-width') || '400px';
        document.documentElement.style.setProperty('--map-width', savedMapWidth);

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            vehicleId = parseInt(urlParams.get('vehicleId'));
            token = urlParams.get('token');

            // Set container height
            setContainerHeight();

            // Initialize map
            initializeMap();

            // Initialize resizer
            initializeResizer();

            if (!vehicleId || !token) {
                showToastError('Accesso non valido. Sono richiesti ID veicolo e token.');
                return;
            }

            // Set default date range (today from 00:00 to tomorrow 00:00)
            setDefaultDateRange();

            // Load vehicle info and initial messages
            loadVehicleInfo();
            loadMessages();
        });

        // Initialize map
        function initializeMap() {
            if (map) return;

            map = L.map('map').setView([54.6872, 25.2797], 7); // Default to Lithuania

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            map.invalidateSize();

            setTimeout(() => map.invalidateSize(), 100);
        }

        function updateMap(messages) {
            if (!map) return;

            // Clear existing markers and path
            map.eachLayer((layer) => {
                if (layer instanceof L.Marker || layer instanceof L.Polyline) {
                    map.removeLayer(layer);
                }
            });

            // Add tile layer back if removed
            if (!map.hasLayer(L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'))) {
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
            }

            const bounds = [];
            const pathPoints = [];
            pathMarkers = []; // Reset path markers array

            messages.forEach((msg, index) => {
                if (msg.latitude && msg.longitude) {
                    const lat = parseFloat(msg.latitude);
                    const lng = parseFloat(msg.longitude);

                    // Create larger marker for path points
                    const marker = L.marker([lat, lng], {
                        icon: L.divIcon({
                            className: 'path-marker',
                            html: `<div style="background-color: #3498db; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
                            iconSize: [16, 16],
                            iconAnchor: [8, 8]
                        })
                    })
                    .bindPopup(`
                        <div style="text-align: center;">
                            <strong>Messaggio ${index + 1}</strong><br>
                            <strong>Ora:</strong> ${new Date(msg.device_timestamp).toLocaleString()}<br>
                            <strong>Velocità:</strong> ${parseFloat(msg.speed).toFixed(1)} km/h<br>
                            <strong>Direzione:</strong> ${parseFloat(msg.direction).toFixed(2)}°<br>
                            <strong>Accensione:</strong> ${msg.ignition_status ? 'ACCESO' : 'SPENTO'}<br>
                            <strong>Coordinate:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}
                        </div>
                    `);

                    marker.addTo(map);
                    marker.on('click', () => {
                        selectMessageFromMap(index);
                    });

                    pathMarkers.push(marker);
                    bounds.push([lat, lng]);
                    pathPoints.push([lat, lng]);
                }
            });

            // Draw path if multiple points
            if (pathPoints.length > 1) {
                const polyline = L.polyline(pathPoints, {
                    color: '#3498db',
                    weight: 4,
                    opacity: 0.8,
                    smoothFactor: 1
                }).addTo(map);
            }

            // Fit map to show all points only if no current selection
            if (bounds.length > 0 && selectedRowIndex === null) {
                map.fitBounds(bounds, { padding: [30, 30] });
            }
        }

        function initializeResizer() {
            const divider = document.querySelector('.divider');
            divider.addEventListener('mousedown', (e) => {
                isResizing = true;
                lastX = e.clientX;
                document.body.style.cursor = 'ew-resize';
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e) => {
                if (!isResizing) return;
                const dx = e.clientX - lastX;
                const mapContainer = document.querySelector('.map-container');
                const currentWidth = mapContainer.offsetWidth;
                const newWidth = Math.max(200, Math.min(currentWidth + dx, window.innerWidth - 300));
                document.documentElement.style.setProperty('--map-width', newWidth + 'px');
                if (map) map.invalidateSize();
                lastX = e.clientX;
            });

            document.addEventListener('mouseup', (e) => {
                if (isResizing) {
                    isResizing = false;
                    document.body.style.cursor = '';
                    const mapContainer = document.querySelector('.map-container');
                    const currentWidth = mapContainer.offsetWidth;
                    localStorage.setItem('vehicle-messages-map-width', currentWidth + 'px');
                }
            });
        }

        function setDefaultDateRange() {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');

            // Get today's date in local timezone at 00:00
            const today = new Date();
            const tomorrow = new Date(today);

            // Set tomorrow to next day, still at 00:00 local time
            tomorrow.setDate(tomorrow.getDate() + 1);

            // Format for datetime-local input (YYYY-MM-DDTHH:mm)
            const startDateString = today.getFullYear() + '-' +
                String(today.getMonth() + 1).padStart(2, '0') + '-' +
                String(today.getDate()).padStart(2, '0') + 'T' +
                '00:00';

            const endDateString = tomorrow.getFullYear() + '-' +
                String(tomorrow.getMonth() + 1).padStart(2, '0') + '-' +
                String(tomorrow.getDate()).padStart(2, '0') + 'T' +
                '00:00';

            startDateInput.value = startDateString;
            endDateInput.value = endDateString;
        }

        async function loadVehicleInfo() {
            try {
                const response = await fetch(`/api/admin/vehicles/${vehicleId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success && data.data) {
                    const vehicle = data.data;
                    // Display vehicle name and description
                    let vehicleInfo = `${vehicle.name}`;
                    if (vehicle.description) {
                        vehicleInfo += ` (${vehicle.description})`;
                    }
                    document.getElementById('vehicleTitle').textContent = `Messaggi per ${vehicleInfo}`;
                } else {
                    document.getElementById('vehicleTitle').textContent = `Messaggi per Veicolo (ID: ${vehicleId})`;
                }
            } catch (error) {
                console.error('Error loading vehicle info:', error);
                    document.getElementById('vehicleTitle').textContent = `Messaggi per Veicolo (ID: ${vehicleId})`;
            }
        }

        async function loadMessages() {
            // Validate date range before proceeding
            if (!validateDateRange()) {
                return;
            }

            const container = document.getElementById('messagesContainer');
            container.innerHTML = '<div class="loading">Caricamento messaggi...</div>';

            try {
                const startDateInput = document.getElementById('startDate');
                const endDateInput = document.getElementById('endDate');
                const sensorFilterInput = document.getElementById('sensorFilter');

                const params = new URLSearchParams({
                    limit: '1000', // Load more than needed to allow for sensor filtering
                    offset: '0',
                    orderBy: currentSortField,
                    order: currentSortOrder
                });

                if (startDateInput.value) {
                    // Convert local datetime to UTC for database query
                    // Use the user's timezone (Europe/Rome by default)
                    const startDate = new Date(startDateInput.value);
                    params.append('startDate', startDate.toISOString());
                }

                if (endDateInput.value) {
                    // Convert local datetime to UTC for database query
                    // Use the user's timezone (Europe/Rome by default)
                    const endDate = new Date(endDateInput.value);
                    params.append('endDate', endDate.toISOString());
                }

                const response = await fetch(`/api/admin/vehicles/${vehicleId}/messages?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    let messages = data.data;

                    // Apply sensor filter if specified
                    const sensorFilterText = sensorFilterInput.value.trim();
                    if (sensorFilterText) {
                        messages = messages.filter(message => {
                            const sensorData = formatSensorData(message.variable_data);
                            return sensorData.toLowerCase().includes(sensorFilterText.toLowerCase());
                        });
                    }

                    // Limit results to selected limit
                    const limitedMessages = messages.slice(0, limit);

                    currentMessages = limitedMessages;
                    displayMessages(limitedMessages);
                    updateMap(limitedMessages);
                    map.invalidateSize();
                    currentOffset = 0;
                    hasMore = messages.length > limit;

                    // Auto-select first message if available
                    if (limitedMessages.length > 0) {
                        setTimeout(() => {
                            selectRow(0);
                        }, 200);
                    }
                } else {
                    showToastError(data.message || 'Impossibile caricare i messaggi');
                    container.innerHTML = '<div class="no-data">Impossibile caricare i messaggi</div>';
                }
            } catch (error) {
                console.error('Error loading messages:', error);
                showToastError('Impossibile caricare i messaggi');
                container.innerHTML = '<div class="no-data">Errore nel caricamento dei messaggi</div>';
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('messagesContainer');

            if (messages.length === 0) {
                container.innerHTML = '<div class="no-data">Nessun messaggio trovato per il periodo selezionato</div>';
                return;
            }

            const tableHTML = `
                <div class="table-wrapper">
                    <div class="table-scroll-container">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th class="sortable" onclick="toggleSort('device_timestamp')" style="cursor: pointer;">
                                            Timestamp Dispositivo
                                            <span id="device-timestamp-sort" class="sort-indicator">↓</span>
                                        </th>
                                        <th class="sortable" onclick="toggleSort('server_timestamp')" style="cursor: pointer;">
                                            Timestamp Server
                                            <span id="server-timestamp-sort" class="sort-indicator">↓</span>
                                        </th>
                                        <th>Latitudine</th>
                                        <th>Longitudine</th>
                                        <th>Velocità (km/h)</th>
                                        <th>Direzione (°)</th>
                                        <th>Accensione</th>
                                        <th>Altitudine (m)</th>
                                        <th>Satelliti</th>
                                        <th>Batteria V</th>
                                        <th>Voltaggio Esterno V</th>
                                        <th>Contachilometri Totale (km)</th>
                                        <th>Sensori</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${messages.map((msg, index) => `
                                        <tr>
                                            <td>${index + 1}</td>
                                            <td>${new Date(msg.device_timestamp).toLocaleString()}</td>
                                            <td>${new Date(msg.server_timestamp).toLocaleString()}</td>
                                            <td>${parseFloat(msg.latitude).toFixed(6)}</td>
                                            <td>${parseFloat(msg.longitude).toFixed(6)}</td>
                                            <td>${parseFloat(msg.speed).toFixed(0)}</td>
                                            <td>${parseFloat(msg.direction).toFixed(0)}</td>
                                            <td>${msg.ignition_status ? 'ON' : 'OFF'}</td>
                                            <td>${parseFloat(msg.altitude).toFixed(0)}</td>
                                            <td>${msg.satellites}</td>
                                            <td>${msg.battery_voltage ? parseFloat(msg.battery_voltage).toFixed(1) : '0.0'}</td>
                                            <td>${msg.external_voltage ? parseFloat(msg.external_voltage).toFixed(1) : '0.0'}</td>
                                            <td>${msg.total_odometer ? parseFloat(msg.total_odometer).toFixed(1) : '0.0'}</td>
                                            <td>${formatSensorData(msg.variable_data)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="pagination">
                    <div>Mostrando ${messages.length} messaggi</div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <label for="messageLimit" style="font-size: 0.9rem; color: #666;">Messaggi:</label>
                        <select id="messageLimit" onchange="changeLimit(this.value)" style="padding: 0.3rem 0.5rem; border: 1px solid #ddd; border-radius: 4px; font-size: 0.9rem;">
                            <option value="100" ${limit === 100 ? 'selected' : ''}>100</option>
                            <option value="500" ${limit === 500 ? 'selected' : ''}>500</option>
                            <option value="1000" ${limit === 1000 ? 'selected' : ''}>1000</option>
                        </select>
                    </div>
                </div>
            `;

            container.innerHTML = tableHTML;

            // Add event listeners to table rows and headers after rendering
            setTimeout(() => {
                addRowClickListeners();
                addSortButtonListeners();
            }, 100);
        }

        async function loadMoreMessages() {
            if (!hasMore) return;

            try {
                const startDateInput = document.getElementById('startDate');
                const endDateInput = document.getElementById('endDate');

                const params = new URLSearchParams({
                    limit: limit.toString(),
                    offset: (currentOffset + limit).toString(),
                    orderBy: currentSortField,
                    order: currentSortOrder
                });

                if (startDateInput.value) {
                    const startDate = new Date(startDateInput.value);
                    params.append('startDate', startDate.toISOString());
                }

                if (endDateInput.value) {
                    const endDate = new Date(endDateInput.value);
                    params.append('endDate', endDate.toISOString());
                }

                const response = await fetch(`/api/admin/vehicles/${vehicleId}/messages?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Add new messages to current array
                    currentMessages = currentMessages.concat(data.data);

                    // Append new messages to table
                    const tbody = document.querySelector('#messagesContainer .table-container table tbody');
                    const pagination = document.querySelector('.pagination');

                    data.data.forEach((msg, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${currentOffset + limit + index + 1}</td>
                            <td>${new Date(msg.device_timestamp).toLocaleString()}</td>
                            <td>${new Date(msg.server_timestamp).toLocaleString()}</td>
                            <td>${parseFloat(msg.latitude).toFixed(6)}</td>
                            <td>${parseFloat(msg.longitude).toFixed(6)}</td>
                            <td>${parseFloat(msg.speed).toFixed(0)}</td>
                            <td>${parseFloat(msg.direction).toFixed(0)}</td>
                            <td>${msg.ignition_status ? 'ON' : 'OFF'}</td>
                            <td>${parseFloat(msg.altitude).toFixed(0)}</td>
                            <td>${msg.satellites}</td>
                            <td>${msg.battery_voltage ? parseFloat(msg.battery_voltage).toFixed(1) : '0.0'}</td>
                            <td>${msg.external_voltage ? parseFloat(msg.external_voltage).toFixed(1) : '0.0'}</td>
                            <td>${msg.total_odometer ? parseFloat(msg.total_odometer).toFixed(1) : '0.0'}</td>
                            <td>${formatSensorData(msg.variable_data)}</td>
                        `;
                        tbody.appendChild(row);
                    });

                    // Update map with new messages
                    updateMap(currentMessages);

                    currentOffset += limit;
                    hasMore = data.pagination ? data.pagination.hasMore : false;

                    // Update pagination info
                    const infoDiv = pagination.querySelector('div');
                    infoDiv.textContent = `Mostrando ${currentOffset + data.data.length} messaggi${hasMore ? ' (altri disponibili)' : ''}`;

                    if (!hasMore) {
                        // Remove load more button
                        const loadMoreBtn = pagination.querySelector('button');
                        if (loadMoreBtn) loadMoreBtn.remove();
                    }
                } else {
                    showToastError(data.message || 'Impossibile caricare altri messaggi');
                }
            } catch (error) {
                console.error('Error loading more messages:', error);
                showToastError('Impossibile caricare altri messaggi');
            }
        }

        function clearFilters() {
            // Reset to default date range instead of clearing completely
            setDefaultDateRange();
            loadMessages();
        }

        function setContainerHeight() {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const container = document.querySelector('.container');
            container.style.height = `${window.innerHeight - headerHeight - 10}px`;
        }

        function validateDateRange() {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');

            if (startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);

                if (startDate >= endDate) {
                    showToastError('La data di inizio deve essere precedente alla data di fine');
                    // Reset the end date to be after start date
                    endDateInput.value = '';
                    return false;
                }
            }
            return true;
        }

        // Add click event listeners to table rows
        function addRowClickListeners() {
            const table = document.querySelector('#messagesContainer .table-container table');
            if (!table) return;

            const rows = table.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.cursor = 'pointer';
                row.addEventListener('click', () => {
                    selectRow(index);
                });
            });
        }

        // Add click event listeners to sortable table headers
        function addSortButtonListeners() {
            const table = document.querySelector('#messagesContainer .table-container table');
            if (!table) return;

            const sortableHeaders = table.querySelectorAll('th.sortable');
            sortableHeaders.forEach(header => {
                const onclick = header.getAttribute('onclick');
                if (onclick) {
                    header.addEventListener('click', () => {
                        eval(onclick); // Execute the onclick function
                    });
                }
            });
        }

        // Select/deselect a table row
        function selectRow(rowIndex) {
            // Remove previous selection
            if (selectedRowIndex !== null) {
                const prevRow = document.querySelector(`#messagesContainer .table-container table tbody tr:nth-child(${selectedRowIndex + 1})`);
                if (prevRow) {
                    prevRow.classList.remove('selected');
                }
            }

            // Set new selection
            selectedRowIndex = rowIndex;
            const currentRow = document.querySelector(`#messagesContainer .table-container table tbody tr:nth-child(${rowIndex + 1})`);
            if (currentRow) {
                currentRow.classList.add('selected');
            }

            // Show message position on map
            showMessageOnMap(rowIndex);
        }

        // Show message position on map (without changing zoom)
        function showMessageOnMap(messageIndex) {
            if (!currentMessages[messageIndex] || !map) return;

            const message = currentMessages[messageIndex];

            // Check if message has valid coordinates
            if (!message.latitude || !message.longitude) {
                showToastError('Questo messaggio non ha coordinate GPS valide');
                return;
            }

            const lat = parseFloat(message.latitude);
            const lng = parseFloat(message.longitude);

            // Clear existing selected markers (not path markers)
            map.eachLayer((layer) => {
                if (layer instanceof L.Marker && !pathMarkers.includes(layer)) {
                    map.removeLayer(layer);
                }
            });

            // Add new marker for selected message with different style
            const marker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'selected-marker',
                    html: `<div style="background-color: #e74c3c; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.4);"></div>`,
                    iconSize: [22, 22],
                    iconAnchor: [11, 11]
                })
            })
            .bindPopup(`
                <div style="text-align: center;">
                    <strong>Messaggio 1</strong><br>
                    <strong>Ora:</strong> ${new Date(message.device_timestamp).toLocaleString()}<br>
                    <strong>Velocità:</strong> ${parseFloat(message.speed).toFixed(1)} km/h<br>
                    <strong>Direzione:</strong> ${parseFloat(message.direction).toFixed(2)}°<br>
                    <strong>Accensione:</strong> ${message.ignition_status ? 'ACCESO' : 'SPENTO'}<br>
                    <strong>Coordinate:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}
                </div>
            `)
            .addTo(map);

            // Center map on selected position without changing zoom
            map.setView([lat, lng]);

            // Open popup
            marker.openPopup();
        }

        // Select message from map click
        function selectMessageFromMap(messageIndex) {
            // Update table selection
            selectRow(messageIndex);

            // Scroll the selected row into view
            setTimeout(() => {
                const selectedRow = document.querySelector(`#messagesContainer .table-container table tbody tr:nth-child(${messageIndex + 1})`);
                if (selectedRow) {
                    selectedRow.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }, 100);

            // Close any open popups first
            map.closePopup();

            // Open popup for the selected marker
            setTimeout(() => {
                const selectedMarker = pathMarkers[messageIndex];
                if (selectedMarker) {
                    selectedMarker.openPopup();
                }
            }, 150);
        }

        // Change message limit and reload
        function changeLimit(newLimit) {
            limit = parseInt(newLimit);
            loadMessages();
        }

        // Toggle sorting for table columns
        function toggleSort(field) {
            // If clicking on the same field, toggle order
            if (currentSortField === field) {
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                // If clicking on a different field, keep the current sort order
                currentSortField = field;
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            }

            // Update sort indicators
            updateSortIndicators();

            // Reload messages with new sorting
            loadMessages();
        }

        // Update sort indicators in table headers
        function updateSortIndicators() {
            // Reset all indicators
            const indicators = document.querySelectorAll('.sort-indicator');
            indicators.forEach(indicator => {
                indicator.textContent = '↕';
                indicator.style.opacity = '0.3';
            });

            // Set active indicator
            const activeIndicator = document.getElementById(`${currentSortField}-sort`);
            if (activeIndicator) {
                activeIndicator.textContent = currentSortOrder === 'ASC' ? '↑' : '↓';
                activeIndicator.style.opacity = '1';
            }
        }

        // Format sensor data from variable_data field
        function formatSensorData(variableData) {
            if (!variableData || !variableData.elements) {
                return '-';
            }

            try {
                const elements = variableData.elements;
                const sensorPairs = [];

                // Iterate through all elements and format them as io_id=value
                Object.keys(elements).forEach(ioId => {
                    const value = elements[ioId];
                    if (value !== null && value !== undefined) {
                        sensorPairs.push(`io_${ioId}=${value}`);
                    }
                });

                return sensorPairs.length > 0 ? sensorPairs.join(';') : '-';
            } catch (error) {
                console.warn('Error formatting sensor data:', error);
                return '-';
            }
        }



        // Display filtered messages (without pagination since it's client-side filtering)
        function displayFilteredMessages(messages) {
            const container = document.getElementById('messagesContainer');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="table-wrapper">
                        <div class="table-scroll-container">
                            <div class="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th class="sortable" onclick="toggleSort('device_timestamp')" style="cursor: pointer;">
                                                Timestamp Dispositivo
                                                <span id="device-timestamp-sort" class="sort-indicator">↓</span>
                                            </th>
                                            <th class="sortable" onclick="toggleSort('server_timestamp')" style="cursor: pointer;">
                                                Timestamp Server
                                                <span id="server-timestamp-sort" class="sort-indicator">↓</span>
                                            </th>
                                            <th>Latitudine</th>
                                            <th>Longitudine</th>
                                            <th>Velocità (km/h)</th>
                                            <th>Direzione (°)</th>
                                            <th>Accensione</th>
                                            <th>Altitudine (m)</th>
                                            <th>Satelliti</th>
                                            <th>Batteria V</th>
                                            <th>Voltaggio Esterno V</th>
                                            <th>Contachilometri Totale (km)</th>
                                            <th>Sensori</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="14" style="text-align: center; color: #666; font-style: italic;">
                                                Nessun messaggio trovato con il filtro sensori: "${sensorFilterText}"
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="pagination">
                        <div>
                            ${currentMessages.length} messaggi totali, 0 filtrati
                        </div>
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <button class="btn btn-secondary" onclick="clearSensorFilter()" style="font-size: 0.9rem;">Rimuovi Filtro</button>
                        </div>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <div class="table-wrapper">
                    <div class="table-scroll-container">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th class="sortable" onclick="toggleSort('device_timestamp')" style="cursor: pointer;">
                                            Timestamp Dispositivo
                                            <span id="device-timestamp-sort" class="sort-indicator">↓</span>
                                        </th>
                                        <th class="sortable" onclick="toggleSort('server_timestamp')" style="cursor: pointer;">
                                            Timestamp Server
                                            <span id="server-timestamp-sort" class="sort-indicator">↓</span>
                                        </th>
                                        <th>Latitudine</th>
                                        <th>Longitudine</th>
                                        <th>Velocità (km/h)</th>
                                        <th>Direzione (°)</th>
                                        <th>Accensione</th>
                                        <th>Altitudine (m)</th>
                                        <th>Satelliti</th>
                                        <th>Batteria V</th>
                                        <th>Voltaggio Esterno V</th>
                                        <th>Contachilometri Totale (km)</th>
                                        <th>Sensori</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${messages.map((msg, index) => `
                                        <tr>
                                            <td>${index + 1}</td>
                                            <td>${new Date(msg.device_timestamp).toLocaleString()}</td>
                                            <td>${new Date(msg.server_timestamp).toLocaleString()}</td>
                                            <td>${parseFloat(msg.latitude).toFixed(6)}</td>
                                            <td>${parseFloat(msg.longitude).toFixed(6)}</td>
                                            <td>${parseFloat(msg.speed).toFixed(0)}</td>
                                            <td>${parseFloat(msg.direction).toFixed(0)}</td>
                                            <td>${msg.ignition_status ? 'ON' : 'OFF'}</td>
                                            <td>${parseFloat(msg.altitude).toFixed(0)}</td>
                                            <td>${msg.satellites}</td>
                                            <td>${msg.battery_voltage ? parseFloat(msg.battery_voltage).toFixed(1) : '0.0'}</td>
                                            <td>${msg.external_voltage ? parseFloat(msg.external_voltage).toFixed(1) : '0.0'}</td>
                                            <td>${msg.total_odometer ? parseFloat(msg.total_odometer).toFixed(1) : '0.0'}</td>
                                            <td>${formatSensorData(msg.variable_data)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="pagination">
                    <div>
                        ${currentMessages.length} messaggi totali, ${messages.length} filtrati per "${sensorFilterText}"
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <button class="btn btn-secondary" onclick="clearSensorFilter()" style="font-size: 0.9rem;">Rimuovi Filtro</button>
                    </div>
                </div>
            `;

            container.innerHTML = tableHTML;

            // Add event listeners to table rows after rendering
            setTimeout(() => {
                addRowClickListeners();
            }, 100);
        }

        // Clear sensor filter and show all messages
        function clearSensorFilter() {
            const sensorFilterInput = document.getElementById('sensorFilter');
            sensorFilterInput.value = '';
            sensorFilterText = '';

            displayMessages(currentMessages);
            updateMap(currentMessages);
        }

    </script>
</body>
</html>
