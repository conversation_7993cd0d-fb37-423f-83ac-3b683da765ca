<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pannello Amministratore - Gestione Utenti e Veicoli</title>
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <link rel="stylesheet" href="toast.css">
    <link rel="stylesheet" href="color-system.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .header {
            background: var(--brand-900);
            color: var(--color-text-on-dark);
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.1),
                0 0 40px rgba(0, 0, 0, 0.05);
        }

        .header h1 {
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 316px;
            height: 60px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            background: var(--color-primary);
            color: var(--color-text-on-primary);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn:focus {
            outline: 3px solid var(--focus-ring);
            outline-offset: 2px;
        }

        .btn-danger {
            background: var(--color-error);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--color-success);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .admin-section {
            display: none;
        }

        .admin-section.active {
            display: block;
        }

        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 0 40px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header .logo {
            width: 315px;
            height: 65px;
            margin-bottom: 0rem;
        }

        .login-header h1 {
            font-size: 2.5rem;
            color: var(--brand-900);
            margin: 0;
            font-weight: 700;
        }

        .card .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card .header-section h2 {
            margin: 0;
            color: #2c3e50;
        }

        .card .divider {
            height: 3px;
            background-color: #eee;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .users-grid {
            display: flex;
            flex-direction: row;
            gap: 0.5rem;
            max-height: 500px;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        .user-card {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            min-width: 180px;
            max-width: 200px;
            flex-shrink: 0;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 4px rgba(0, 0, 0, 0.06),
                0 0 20px rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;
        }

        .user-card.admin {
            border-left-color: #e74c3c;
        }

        .user-card.selected {
            background: #e8f4f8;
            border-left-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
        }

        .user-card h3 {
            margin-bottom: 0.25rem;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .user-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.2rem;
            font-size: 0.8rem;
        }

        .user-actions {
            margin-top: 0.5rem;
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .user-actions button {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .vehicle-card {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 8px;
            border-left: 4px solid #2ecc71;
            min-width: 180px;
            max-width: 280px;
            flex-shrink: 0;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 4px rgba(0, 0, 0, 0.06),
                0 0 20px rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;
        }

        .vehicle-card.selected {
            background: #e8f4fd;
            border-left-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
        }

        .vehicle-card h3 {
            margin-bottom: 0.25rem;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .vehicle-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.2rem;
            font-size: 0.8rem;
        }

        .vehicle-actions {
            margin-top: 0.5rem;
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .vehicle-actions button {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }


    </style>
</head>
<body>
    <div class="header">
        <h1>
            <img src="images/logo-ecotrac-bianco.png" alt="Logo" class="logo">
        </h1>
        <div class="user-info" style="display: flex; gap: 20px; align-items: center;">
            <span id="username"></span>
            <button class="btn btn-danger" onclick="logout()" style="display: none;" id="logoutBtn">Esci</button>
        </div>
    </div>

    <!-- Admin Section -->
    <div id="adminSection" class="admin-section">
        <div class="container">
            <div id="adminError" class="error" style="display: none;"></div>
            <div id="adminSuccess" class="success" style="display: none;"></div>

            <div class="card">
                <div class="header-section">
                    <h2>Gestisci Utenti</h2>
                    <button class="btn" onclick="showAddUserForm()">Aggiungi Nuovo Utente</button>
                </div>
                <div class="divider"></div>
                <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                    <input type="text" id="filterUsers" placeholder="Usa * come jolly (es. *utente* o utente*)" style="padding: 0.5rem 1rem; width: 250px; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem;">
                    <label for="filterAdmin" style="font-size: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" id="filterAdmin">
                        Solo Utenti Amministratori
                    </label>
                </div>
                <div id="usersContainer" class="loading">Caricamento utenti...</div>
            </div>

            <div class="card hidden" id="addUserCard">
                <h2>Aggiungi Nuovo Utente</h2>
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="newUsername">Username:</label>
                        <input type="text" id="newUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">Password:</label>
                        <input type="password" id="newPassword" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="newAccountType">Account Type:</label>
                        <select id="newAccountType" name="account_type" required>
                            <option value="normal">Normal User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">Add</button>
                    <button type="button" class="btn" onclick="cancelAdd()">Cancel</button>
                </form>
            </div>

            <div class="card hidden" id="editUserCard">
                <h2>Modifica Utente</h2>
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="form-group">
                        <label for="editUsername">Username:</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editPassword">Password:</label>
                        <input type="password" id="editPassword" name="password">
                    </div>
                    <div class="form-group">
                        <label for="editAccountType">Account Type:</label>
                        <select id="editAccountType" name="account_type" required>
                            <option value="normal">Normal User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">Save</button>
                    <button type="button" class="btn" onclick="cancelEdit()">Cancel</button>
                </form>
            </div>

            <!-- Vehicle Management Section (shown when user is selected) -->
            <div id="vehiclesManagement" class="hidden">
                <div id="vehiclesError" class="error" style="display: none;"></div>
                <div id="vehiclesSuccess" class="success" style="display: none;"></div>

                <div class="card">
                    <div class="header-section">
                        <h2 id="vehicleSectionTitle">Gestisci Veicoli</h2>
                        <button class="btn" onclick="showAddVehicleForm()" id="addVehicleBtn">Aggiungi Nuovo Veicolo</button>
                    </div>
                    <div class="divider"></div>
                    <div id="vehiclesContainer" class="loading">Seleziona un utente per vedere i suoi veicoli</div>
                </div>

                <div class="card hidden" id="addVehicleCard">
                <h2>Aggiungi Nuovo Veicolo</h2>
                    <form id="addVehicleForm">
                        <div class="form-group">
                            <label for="newVehicleImei">IMEI:</label>
                            <input type="text" id="newVehicleImei" name="imei" maxlength="15" required>
                        </div>
                        <div class="form-group">
                            <label for="newVehicleSimNumber">SIM Number:</label>
                            <input type="text" id="newVehicleSimNumber" name="sim_number" maxlength="20" required>
                        </div>
                        <div class="form-group">
                            <label for="newVehicleName">Name:</label>
                            <input type="text" id="newVehicleName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="newVehicleDescription">Description:</label>
                            <textarea id="newVehicleDescription" name="description" rows="3" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem;"></textarea>
                        </div>
                        <button type="submit" class="btn">Add</button>
                        <button type="button" class="btn" onclick="cancelAddVehicle()">Cancel</button>
                    </form>
                </div>

                <div class="card hidden" id="editVehicleCard">
                    <h2>Modifica Veicolo</h2>
                    <form id="editVehicleForm">
                        <input type="hidden" id="editVehicleId">
                        <div class="form-group">
                            <label for="editVehicleImei">IMEI:</label>
                            <input type="text" id="editVehicleImei" name="imei" maxlength="15" required>
                        </div>
                        <div class="form-group">
                            <label for="editVehicleSimNumber">SIM Number:</label>
                            <input type="text" id="editVehicleSimNumber" name="sim_number" maxlength="20" required>
                        </div>
                        <div class="form-group">
                            <label for="editVehicleName">Name:</label>
                            <input type="text" id="editVehicleName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="editVehicleDescription">Description:</label>
                            <textarea id="editVehicleDescription" name="description" rows="3" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem;"></textarea>
                        </div>
                        <button type="submit" class="btn btn-success">Save</button>
                        <button type="button" class="btn" onclick="cancelEditVehicle()">Cancel</button>
                    </form>
                </div>

                <!-- Vehicle Position Card (shown when vehicle is selected) -->
                <div id="vehiclePositionCard" class="card hidden">
                    <div class="header-section">
                        <h2 id="vehiclePositionTitle">Posizione per veicolo</h2>
                        <button class="btn" onclick="viewVehicleMessages()">Visualizza Messaggi</button>
                    </div>
                    <div class="divider"></div>
                    <div style="display: flex; height: 400px;">
                        <!-- Map on the left -->
                        <div id="vehicleMap" style="flex: 1; border-radius: 4px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);"></div>
                        <!-- Vertical divider -->
                        <div style="width: 2px; background-color: #eee; margin: 0 1rem;"></div>
                        <!-- Data on the right -->
                        <div id="vehicleData" style="flex: 1; padding-left: 1rem;">
                            <div id="vehiclePositionData" class="loading">Loading position...</div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <!-- Login Form -->
    <div id="loginSection">
        <div class="container">
            <div class="card" style="max-width: 400px; margin: 2rem auto;">
                <div class="login-header">
                    <img src="images/logo-ecotrac-verde.png" alt="Logo" class="logo">
                </div>
                <h2 style="text-align: center; margin-bottom: 2rem; color: var(--color-text-secondary);">Accesso Amministratore</h2>
                <div id="loginError" class="error" style="display: none;"></div>
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="loginPassword" name="password" required>
                    </div>
                    <button type="submit" class="btn" style="width: 100%;">Login</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

<script src="toast.js"></script>
<script>
        let token = localStorage.getItem('token');
        let currentUser = null;
        let allUsers = [];
        let selectedUser = null;
        let selectedUserName = '';
        let userVehicles = [];
        let selectedVehicle = null;
        let selectedVehicleName = '';
        let vehiclePosition = null;
        let vehicleMap = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            if (token) {
                verifyToken();
            } else {
                showLogin();
            }
        });

        // Show login form
        function showLogin() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('adminSection').classList.remove('active');
            document.getElementById('logoutBtn').style.display = 'none';
        }

        // Show admin section
        function showAdmin() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('adminSection').classList.add('active');
            document.getElementById('logoutBtn').style.display = 'block';
            loadUsers();
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    currentUser = data.data.user;
                    document.getElementById('username').textContent = data.data.user.username;
                    
                    // Verify that user is admin
                    const verifyResponse = await fetch('/api/auth/verify', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    const verifyData = await verifyResponse.json();
                    
                    if (verifyData.success && verifyData.data.user.account_type === 'admin') {
                        showAdmin();
                    } else {
                        showToastError('Access denied. Admin privileges required.');
                        localStorage.removeItem('token');
                        token = null;
                    }
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Login failed. Please try again.');
            }
        });

        // Verify token
        async function verifyToken() {
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentUser = data.data.user;
                    document.getElementById('username').textContent = data.data.user.username;
                    
                    // Check if user is admin
                    if (data.data.user.account_type === 'admin') {
                        showAdmin();
                    } else {
                        showToastError('Access denied. Admin privileges required.');
                        localStorage.removeItem('token');
                        token = null;
                        showLogin();
                    }
                } else {
                    localStorage.removeItem('token');
                    token = null;
                    showLogin();
                }
            } catch (error) {
                localStorage.removeItem('token');
                token = null;
                showLogin();
            }
        }

        // Logout
        function logout() {
            localStorage.removeItem('token');
            token = null;
            currentUser = null;
            showLogin();
        }

        // Load all users
        async function loadUsers() {
            try {
                const response = await fetch('/api/admin/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    allUsers = data.data;
                    applyFilter();
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Failed to load users.');
            }
        }

        // Display users in grid
        function displayUsers(users) {
            const container = document.getElementById('usersContainer');

            if (users.length === 0) {
                container.innerHTML = '<p>Nessun utente trovato.</p>';
                return;
            }

            container.innerHTML = '';
            container.className = 'users-grid';

            users.forEach(user => {
                const card = document.createElement('div');
                card.className = `user-card ${user.account_type === 'admin' ? 'admin' : ''} ${selectedUser === user.id ? 'selected' : ''}`;

                card.innerHTML = `
                    <h3>${user.username}</h3>
                    <div class="user-info-row">
                        <span>Units:</span>
                        <span>${user.vehicle_count || 0}</span>
                    </div>
                    <div class="user-info-row">
                        <span>Type:</span>
                        <span>${user.account_type}</span>
                    </div>
                    <div class="user-info-row">
                        <span>Created:</span>
                        <span>${new Date(user.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="user-actions">
                        <button class="btn" onclick="editUser(${user.id}, '${user.username}', '${user.account_type}')">Edit</button>
                        ${user.id === currentUser.id ? '' : '<button class="btn btn-danger" onclick="deleteUser(' + user.id + ')">Delete</button>'}
                    </div>
                `;

                // Make the card clickable to select user
                card.addEventListener('click', function(event) {
                    // Prevent selection if clicking on action buttons
                    if (event.target.tagName === 'BUTTON') return;
                    selectUser(user.id, user.username);
                });

                container.appendChild(card);
            });
        }

        // Add new user
        document.getElementById('addUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const account_type = document.getElementById('newAccountType').value;
            
            try {
                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password, account_type })
                });
                
                const data = await response.json();

                if (data.success) {
                    showToastSuccess('User added successfully');
                    document.getElementById('addUserForm').reset();
                    document.getElementById('addUserCard').classList.add('hidden');
                    loadUsers(); // Reload user list

                    // Auto-select the newly added user
                    if (data.data && data.data.id && data.data.username) {
                        selectUser(data.data.id, data.data.username);
                    }
                } else {
                    showToastError(data.message || 'Failed to add user');
                }
            } catch (error) {
                showToastError('Failed to add user');
            }
        });

        // Edit user
        function editUser(userId, username, accountType) {
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUsername').value = username;
            document.getElementById('editAccountType').value = accountType;
            document.getElementById('editUserCard').classList.remove('hidden');
        }

        // Cancel editing
        function cancelEdit() {
            document.getElementById('editUserCard').classList.add('hidden');
        }

        // Update user
        document.getElementById('editUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const userId = document.getElementById('editUserId').value;
            const username = document.getElementById('editUsername').value;
            const password = document.getElementById('editPassword').value;
            const account_type = document.getElementById('editAccountType').value;

            const updateData = { username, account_type };
            if (password.trim() !== '') {
                updateData.password = password;
            }

            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();

                if (data.success) {
                    showToastSuccess('User updated successfully');
                    document.getElementById('editUserCard').classList.add('hidden');
                    loadUsers(); // Reload user list
                } else {
                    showToastError(data.message || 'Failed to update user');
                }
            } catch (error) {
                showToastError('Failed to update user');
            }
        });


        // Delete user
        async function deleteUser(userId) {
            const confirmed = await showConfirmToast('Are you sure you want to delete this user?');
            if (!confirmed) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showToastSuccess('User deleted successfully');
                    loadUsers(); // Reload user list
                } else {
                    // Handle specific error for users with associated vehicles
                    if (data.message && data.message.includes('Cannot delete user:')) {
                        showToastError(data.message);
                    } else {
                        showToastError(data.message || 'Failed to delete user.');
                    }
                }
            } catch (error) {
                showToastError('Failed to delete user.');
            }
        }

        // Show add user form
        function showAddUserForm() {
            document.getElementById('addUserCard').classList.remove('hidden');
        }

        // Cancel add user
        function cancelAdd() {
            document.getElementById('addUserCard').classList.add('hidden');
            document.getElementById('addUserForm').reset();
        }

        // Apply filter
        function applyFilter() {
            const filterValue = document.getElementById('filterUsers').value.trim();
            const onlyAdmin = document.getElementById('filterAdmin').checked;

            let filteredUsers = allUsers;

            // Filter by username if there's a filter value
            if (filterValue) {
                // Convert * to .* for regex, and handle start/end
                let pattern = filterValue.replace(/\*/g, '.*');
                const regex = new RegExp(pattern, 'i'); // case insensitive
                filteredUsers = filteredUsers.filter(user => regex.test(user.username));
            }

            // Filter by admin status if checkbox is checked
            if (onlyAdmin) {
                filteredUsers = filteredUsers.filter(user => user.account_type === 'admin');
            }

            displayUsers(filteredUsers);
        }

        // Event listener for filter input
        document.getElementById('filterUsers').addEventListener('input', applyFilter);

        // Event listener for admin checkbox
        document.getElementById('filterAdmin').addEventListener('change', applyFilter);

        // Select user for vehicle management
        function selectUser(userId, userName) {
            // Clear previous selection
            selectedUser = null;
            selectedUserName = '';
            userVehicles = [];
            selectedVehicle = null;
            selectedVehicleName = '';
            vehiclePosition = null;

            // Hide vehicle position card when switching users
            document.getElementById('vehiclePositionCard').classList.add('hidden');

            // Set new selection
            selectedUser = userId;
            selectedUserName = userName;

            // Update UI
            document.getElementById('vehicleSectionTitle').textContent = `Manage Vehicles for ${userName}`;
            document.getElementById('vehiclesManagement').classList.remove('hidden');
            document.getElementById('addVehicleBtn').disabled = false;
            document.getElementById('vehiclesContainer').innerHTML = 'Loading vehicles...';

            // Load vehicles for this user
            loadUserVehicles(userId);

            // Re-display users to update selection state
            applyFilter();
        }

        // Load vehicles for selected user
        async function loadUserVehicles(userId) {
            try {
                const response = await fetch(`/api/admin/users/${userId}/vehicles`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    userVehicles = data.data;
                    displayUserVehicles(userVehicles);
                } else {
                    showToastError(data.message || 'Failed to load vehicles');
                    document.getElementById('vehiclesContainer').innerHTML = 'Failed to load vehicles.';
                }
            } catch (error) {
                showToastError('Failed to load vehicles');
                document.getElementById('vehiclesContainer').innerHTML = 'Failed to load vehicles.';
            }
        }

        // Display user vehicles in grid
        function displayUserVehicles(vehicles) {
            const container = document.getElementById('vehiclesContainer');

            if (vehicles.length === 0) {
                container.innerHTML = '<p>Nessun veicolo trovato per questo utente.</p>';
                return;
            }

            container.innerHTML = '';
            container.className = 'users-grid'; // Reuse the same grid class

            vehicles.forEach(vehicle => {
                const card = document.createElement('div');
                card.className = `vehicle-card ${selectedVehicle === vehicle.id ? 'selected' : ''}`;

                card.innerHTML = `
                    <h3>${vehicle.name}</h3>
                    <div class="vehicle-info-row">
                        <span>IMEI:</span>
                        <span>${vehicle.imei}</span>
                    </div>
                    <div class="vehicle-info-row">
                        <span>SIM:</span>
                        <span>${vehicle.sim_number || ''}</span>
                    </div>
                    <div class="vehicle-info-row">
                        <span>Desc:</span>
                        <span>${vehicle.description || ''}</span>
                    </div>
                    <div class="vehicle-info-row">
                        <span>Created:</span>
                        <span>${new Date(vehicle.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="vehicle-actions">
                        <button class="btn" onclick="editVehicle(${vehicle.id}, '${vehicle.imei}', '${(vehicle.sim_number || '').replace(/'/g, "\\'")}', '${vehicle.name.replace(/'/g, "\\'")}', '${(vehicle.description || '').replace(/'/g, "\\'")}')">Edit</button>
                        <button class="btn btn-danger" onclick="deleteVehicle(${vehicle.id})">Delete</button>
                    </div>
                `;

                // Make the card clickable to select vehicle
                card.addEventListener('click', function(event) {
                    // Prevent selection if clicking on action buttons
                    if (event.target.tagName === 'BUTTON') return;
                    selectVehicle(vehicle.id, vehicle.name);
                });

                container.appendChild(card);
            });
        }

        // Add new vehicle for selected user
        document.getElementById('addVehicleForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!selectedUser) {
                showToastError('Nessun utente selezionato');
                return;
            }

            const imei = document.getElementById('newVehicleImei').value;
            const sim_number = document.getElementById('newVehicleSimNumber').value;
            const name = document.getElementById('newVehicleName').value;
            const description = document.getElementById('newVehicleDescription').value;

            try {
                const response = await fetch(`/api/admin/users/${selectedUser}/vehicles`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ imei, sim_number, name, description })
                });

                const data = await response.json();

                if (data.success) {
                    showToastSuccess('Vehicle added successfully');
                    document.getElementById('addVehicleForm').reset();
                    document.getElementById('addVehicleCard').classList.add('hidden');
                    loadUserVehicles(selectedUser); // Reload user's vehicle list
                    loadUsers(); // Update user count
                } else {
                    showToastError(data.message || 'Failed to add vehicle');
                }
            } catch (error) {
                showToastError('Failed to add vehicle');
            }
        });

        // Edit vehicle
        function editVehicle(vehicleId, imei, simNumber, name, description) {
            document.getElementById('editVehicleId').value = vehicleId;
            document.getElementById('editVehicleImei').value = imei;
            document.getElementById('editVehicleSimNumber').value = simNumber;
            document.getElementById('editVehicleName').value = name;
            document.getElementById('editVehicleDescription').value = description;
            document.getElementById('editVehicleCard').classList.remove('hidden');
        }

        // Cancel editing vehicle
        function cancelEditVehicle() {
            document.getElementById('editVehicleCard').classList.add('hidden');
        }

        // Update vehicle
        document.getElementById('editVehicleForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!selectedUser) {
                showToastError('No user selected');
                return;
            }

            const vehicleId = document.getElementById('editVehicleId').value;
            const imei = document.getElementById('editVehicleImei').value;
            const sim_number = document.getElementById('editVehicleSimNumber').value;
            const name = document.getElementById('editVehicleName').value;
            const description = document.getElementById('editVehicleDescription').value;

            const updateData = { imei, sim_number, name, description };

            try {
                const response = await fetch(`/api/admin/users/${selectedUser}/vehicles/${vehicleId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();

                if (data.success) {
                    showToastSuccess('Vehicle updated successfully');
                    document.getElementById('editVehicleCard').classList.add('hidden');
                    loadUserVehicles(selectedUser); // Reload user's vehicle list
                } else {
                    showToastError(data.message || 'Failed to update vehicle');
                }
            } catch (error) {
                showToastError('Failed to update vehicle');
            }
        });

        // Delete vehicle
        async function deleteVehicle(vehicleId) {
            const confirmed = await showConfirmToast('Are you sure you want to delete this vehicle?');
            if (!confirmed) {
                return;
            }

            if (!selectedUser) {
                showToastError('No user selected');
                return;
            }

            try {
                const response = await fetch(`/api/admin/users/${selectedUser}/vehicles/${vehicleId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    showToastSuccess('Vehicle deleted successfully');
                    loadUserVehicles(selectedUser); // Reload user's vehicle list
                    loadUsers(); // Update user count
                } else {
                    showToastError(data.message);
                }
            } catch (error) {
                showToastError('Failed to delete vehicle.');
            }
        }

        // Show add vehicle form
        function showAddVehicleForm() {
            if (!selectedUser) {
                showToastError('Seleziona prima un utente');
                return;
            }
            document.getElementById('addVehicleCard').classList.remove('hidden');
        }

        // Cancel add vehicle
        function cancelAddVehicle() {
            document.getElementById('addVehicleCard').classList.add('hidden');
            document.getElementById('addVehicleForm').reset();
        }

        // Select vehicle for position display
        async function selectVehicle(vehicleId, vehicleName) {
            // Clear previous selection
            selectedVehicle = null;
            selectedVehicleName = '';

            // Set new selection
            selectedVehicle = vehicleId;
            selectedVehicleName = vehicleName;

            // Update UI
            document.getElementById('vehiclePositionTitle').textContent = `Ultima posizione per:  ${vehicleName}`;
            document.getElementById('vehiclePositionCard').classList.remove('hidden');
            document.getElementById('vehiclePositionData').textContent = 'Loading position...';

            // Re-display vehicles to update selection state
            displayUserVehicles(userVehicles);

            // Load vehicle position
            await loadVehiclePosition(vehicleId);
        }

        // Load vehicle latest position
        async function loadVehiclePosition(vehicleId) {
            try {
                console.log('Loading position for vehicle ID:', vehicleId);
                console.log('Token present:', !!token);

                const response = await fetch(`/api/admin/vehicles/${vehicleId}/latest-position`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success && data.data) {
                    vehiclePosition = data.data;
                    initMap();
                    displayVehicleData(data.data);
                } else {
                    console.log('No position data found');
                    document.getElementById('vehiclePositionData').innerHTML = '<p class="loading">No position data available</p>';
                    // Initialize map with default location
                    initMap(true);
                }
            } catch (error) {
                console.error('Error loading vehicle position:', error);
                showToastError(`Failed to load vehicle position: ${error.message}`);
                document.getElementById('vehiclePositionData').innerHTML = '<p class="loading">Error loading position</p>';
                initMap(true);
            }
        }

        // Initialize Leaflet map
        function initMap(noData = false) {
            if (vehicleMap) {
                vehicleMap.remove();
            }

            const mapContainer = document.getElementById('vehicleMap');

            // Default location (Rome, Italy) if no data available
            let lat = 41.9028;
            let lng = 12.4964;
            let zoom = 13;

            if (!noData && vehiclePosition) {
                lat = parseFloat(vehiclePosition.latitude);
                lng = parseFloat(vehiclePosition.longitude);
                zoom = 16; // Closer zoom for vehicle position
            }

            vehicleMap = L.map(mapContainer, {
                center: [lat, lng],
                zoom: zoom,
                zoomControl: true,
                attributionControl: true
            });

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(vehicleMap);

            if (!noData && vehiclePosition) {
                // Add marker for vehicle position
                const marker = L.marker([lat, lng]).addTo(vehicleMap);

                // Add popup with vehicle name
                marker.bindPopup(`<b>${selectedVehicleName}</b><br>Lat: ${lat}<br>Lng: ${lng}`).openPopup();
            }

            // Force map to resize
            setTimeout(() => {
                vehicleMap.invalidateSize();
            }, 100);
        }

        // View vehicle messages
        function viewVehicleMessages() {
            if (!selectedVehicle) {
                showToastError('Nessun veicolo selezionato');
                return;
            }

            // Open the vehicle messages page
            window.open(`/vehicle-messages.html?vehicleId=${selectedVehicle}&token=${token}`, '_blank');
        }

        // Display vehicle position data
        function displayVehicleData(position) {
            const container = document.getElementById('vehiclePositionData');

            const formattedDate = new Date(position.device_timestamp).toLocaleString();
            const speed = parseFloat(position.speed) || 0;
            const direction = parseFloat(position.direction) || 0;
            const ignitionStatus = position.ignition_status ? 'ON' : 'OFF';
            const latitude = parseFloat(position.latitude);
            const longitude = parseFloat(position.longitude);
            const odometer = parseFloat(position.odometer) || 0;
            const satellites = parseInt(position.satellites) || 0;
            const altitude = parseFloat(position.altitude) || 0;

            // Fix map initialization with correct numeric values
            vehiclePosition = {
                ...position,
                latitude: latitude,
                longitude: longitude,
                speed: speed,
                direction: direction,
                odometer: odometer,
                satellites: satellites,
                altitude: altitude
            };

            container.innerHTML = `
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px; border-left: 5px solid #3498db; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                    <div class="vehicle-position-grid" style="display: grid; grid-template-columns: 1fr; gap: 0.5rem;">
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">🕒</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Timestamp:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${formattedDate}</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">📍</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Coordinates:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${latitude.toFixed(6)}, ${longitude.toFixed(6)}</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">💨</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Speed:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${speed} km/h</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">🧭</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Direction:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${direction}°</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">${position.ignition_status ? '🔥' : '⭕'}</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Ignition:</span>
                            <span style="font-size: 0.95rem; font-weight: 600; color: ${position.ignition_status ? '#27ae60' : '#e74c3c'}">${ignitionStatus}</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">📏</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Odometer:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${odometer} km</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">🛰️</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Satellites:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${satellites}</span>
                        </div>
                        <div class="vehicle-info-row" style="justify-content: flex-start; gap: 0.75rem; align-items: center; padding: 0.1rem 0;">
                            <span style="font-size: 1.2rem;">⛰️</span>
                            <span style="font-size: 0.9rem; font-weight: 500;">Altitude:</span>
                            <span style="font-size: 0.95rem; font-weight: 600;">${altitude} m</span>
                        </div>
                    </div>
                </div>
            `;

            // Re-initialize map with fixed numeric values
            initMap();
        }


    </script>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
</body>
</html>
